import React, { useState, useEffect } from 'react'
import { useAuth } from '../hooks/useAuth'
import { apiService } from '../services/api'

import Modal from '../components/Modal'
import LoadingSpinner from '../components/LoadingSpinner'

const Users: React.FC = () => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [formData, setFormData] = useState<CreateUserRequest>({
    username: '',
    email: '',
    password: '',
    firstName: '',
    lastName: ''
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { hasPermission } = useAuth()

  const canCreate = hasPermission('Users', 'create')
  const canUpdate = hasPermission('Users', 'update')
  const canDelete = hasPermission('Users', 'delete')

  useEffect(() => {
    fetchUsers()
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      const response = await apiService.getUsers()
      if (response.success && response.data) {
        // Handle the nested structure: response.data.users
        const responseData = response.data as User[] | { users: User[] }
        const usersData = Array.isArray(responseData) ? responseData : responseData.users || []
        setUsers(Array.isArray(usersData) ? usersData : [])
      } else {
        setError(response.message || 'Failed to fetch users')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to fetch users'
      setError(errorMessage || 'Failed to fetch users')
    } finally {
      setLoading(false)
    }
  }

  const handleOpenModal = (user?: User) => {
    if (user) {
      setEditingUser(user)
      setFormData({
        username: user.username,
        email: user.email,
        password: '', // Don't populate password for editing
        firstName: user.firstName,
        lastName: user.lastName
      })
    } else {
      setEditingUser(null)
      setFormData({
        username: '',
        email: '',
        password: '',
        firstName: '',
        lastName: ''
      })
    }
    setFormErrors({})
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingUser(null)
    setFormData({
      username: '',
      email: '',
      password: '',
      firstName: '',
      lastName: ''
    })
    setFormErrors({})
  }

  const validateForm = (): boolean => {
    const errors: FormErrors = {}

    if (!formData.username.trim()) {
      errors.username = 'Username is required'
    } else if (formData.username.length < 3) {
      errors.username = 'Username must be at least 3 characters'
    }

    if (!formData.email.trim()) {
      errors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Email is invalid'
    }

    if (!editingUser && !formData.password) {
      errors.password = 'Password is required'
    } else if (formData.password && formData.password.length < 6) {
      errors.password = 'Password must be at least 6 characters'
    }

    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required'
    }

    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      if (editingUser) {
        // Update user
        const updateData: UpdateUserRequest = {
          username: formData.username,
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName
        }

        // Only include password if it's provided
        if (formData.password) {
          ;(updateData as UpdateUserRequest & { password?: string }).password = formData.password
        }

        const response = await apiService.updateUser(editingUser.id, updateData)
        if (response.success) {
          await fetchUsers()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to update user')
        }
      } else {
        // Create user
        const response = await apiService.createUser(formData)
        if (response.success) {
          await fetchUsers()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to create user')
        }
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to save user'
      setError(errorMessage || 'Failed to save user')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (user: User) => {
    if (!window.confirm(`Are you sure you want to delete user "${user.username}"?`)) {
      return
    }

    try {
      const response = await apiService.deleteUser(user.id)
      if (response.success) {
        await fetchUsers()
      } else {
        setError(response.message || 'Failed to delete user')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to delete user'
      setError(errorMessage || 'Failed to delete user')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Users Management</h1>
        {canCreate && (
          <button onClick={() => handleOpenModal()} className="btn-primary">
            Add User
          </button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-700">{error}</div>
          <button onClick={() => setError('')} className="mt-2 text-sm text-red-600 hover:text-red-800">
            Dismiss
          </button>
        </div>
      )}

      {/* Users table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">Name</th>
                <th className="table-header-cell">Username</th>
                <th className="table-header-cell">Email</th>
                <th className="table-header-cell">Status</th>
                <th className="table-header-cell">Created</th>
                <th className="table-header-cell">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {Array.isArray(users) &&
                users.map(user => (
                  <tr key={user.id}>
                    <td className="table-cell">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                          <span className="text-white text-sm font-medium">
                            {user.firstName[0]}
                            {user.lastName[0]}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {user.firstName} {user.lastName}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">{user.username}</td>
                    <td className="table-cell">{user.email}</td>
                    <td className="table-cell">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {user.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="table-cell">{new Date(user.createdAt).toLocaleDateString()}</td>
                    <td className="table-cell">
                      <div className="flex space-x-2">
                        {canUpdate && (
                          <button
                            onClick={() => handleOpenModal(user)}
                            className="text-blue-600 hover:text-blue-900 text-sm"
                          >
                            Edit
                          </button>
                        )}
                        {canDelete && (
                          <button
                            onClick={() => handleDelete(user)}
                            className="text-red-600 hover:text-red-900 text-sm"
                          >
                            Delete
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>

          {users.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No users found.</p>
            </div>
          )}
        </div>
      </div>

      {/* User form modal */}
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingUser ? 'Edit User' : 'Add User'} size="md">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="firstName" className="form-label">
                First Name
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                className={`form-input ${
                  formErrors.firstName ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                }`}
                value={formData.firstName}
                onChange={handleInputChange}
                required
              />
              {formErrors.firstName && <p className="mt-1 text-sm text-red-600">{formErrors.firstName}</p>}
            </div>

            <div>
              <label htmlFor="lastName" className="form-label">
                Last Name
              </label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                className={`form-input ${
                  formErrors.lastName ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                }`}
                value={formData.lastName}
                onChange={handleInputChange}
                required
              />
              {formErrors.lastName && <p className="mt-1 text-sm text-red-600">{formErrors.lastName}</p>}
            </div>
          </div>

          <div>
            <label htmlFor="username" className="form-label">
              Username
            </label>
            <input
              type="text"
              id="username"
              name="username"
              className={`form-input ${
                formErrors.username ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.username}
              onChange={handleInputChange}
              required
            />
            {formErrors.username && <p className="mt-1 text-sm text-red-600">{formErrors.username}</p>}
          </div>

          <div>
            <label htmlFor="email" className="form-label">
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              className={`form-input ${
                formErrors.email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.email}
              onChange={handleInputChange}
              required
            />
            {formErrors.email && <p className="mt-1 text-sm text-red-600">{formErrors.email}</p>}
          </div>

          <div>
            <label htmlFor="password" className="form-label">
              Password {editingUser && <span className="text-gray-500">(leave blank to keep current)</span>}
            </label>
            <input
              type="password"
              id="password"
              name="password"
              className={`form-input ${
                formErrors.password ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.password}
              onChange={handleInputChange}
              required={!editingUser}
            />
            {formErrors.password && <p className="mt-1 text-sm text-red-600">{formErrors.password}</p>}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={handleCloseModal} className="btn-secondary">
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  {editingUser ? 'Updating...' : 'Creating...'}
                </>
              ) : editingUser ? (
                'Update User'
              ) : (
                'Create User'
              )}
            </button>
          </div>
        </form>
      </Modal>
    </div>
  )
}

export default Users
