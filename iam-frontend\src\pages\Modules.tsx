import React, { useState, useEffect } from 'react'
import { useAuth } from '../hooks/useAuth'
import { apiService } from '../services/api'

import Modal from '../components/Modal'
import LoadingSpinner from '../components/LoadingSpinner'

const Modules: React.FC = () => {
  const [modules, setModules] = useState<Module[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingModule, setEditingModule] = useState<Module | null>(null)
  const [formData, setFormData] = useState<CreateModuleRequest>({
    name: '',
    description: ''
  })
  const [formErrors, setFormErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { hasPermission } = useAuth()

  const canCreate = hasPermission('Modules', 'create')
  const canUpdate = hasPermission('Modules', 'update')
  const canDelete = hasPermission('Modules', 'delete')

  useEffect(() => {
    fetchModules()
  }, [])

  const fetchModules = async () => {
    try {
      setLoading(true)
      const response = await apiService.getModules()
      if (response.success && response.data) {
        // Handle the nested structure: response.data.modules
        const responseData = response.data as Module[] | { modules: Module[] }
        const modulesData = Array.isArray(responseData) ? responseData : responseData.modules || []
        setModules(Array.isArray(modulesData) ? modulesData : [])
      } else {
        setError(response.message || 'Failed to fetch modules')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to fetch modules'
      setError(errorMessage || 'Failed to fetch modules')
    } finally {
      setLoading(false)
    }
  }

  const handleOpenModal = (module?: Module) => {
    if (module) {
      setEditingModule(module)
      setFormData({
        name: module.name,
        description: module.description
      })
    } else {
      setEditingModule(null)
      setFormData({
        name: '',
        description: ''
      })
    }
    setFormErrors({})
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setEditingModule(null)
    setFormData({
      name: '',
      description: ''
    })
    setFormErrors({})
  }

  const validateForm = (): boolean => {
    const errors: FormErrors = {}

    if (!formData.name.trim()) {
      errors.name = 'Module name is required'
    } else if (formData.name.length < 2) {
      errors.name = 'Module name must be at least 2 characters'
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      if (editingModule) {
        // Update module
        const response = await apiService.updateModule(editingModule.id, formData)
        if (response.success) {
          await fetchModules()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to update module')
        }
      } else {
        // Create module
        const response = await apiService.createModule(formData)
        if (response.success) {
          await fetchModules()
          handleCloseModal()
        } else {
          setError(response.message || 'Failed to create module')
        }
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to save module'
      setError(errorMessage || 'Failed to save module')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async (module: Module) => {
    if (!window.confirm(`Are you sure you want to delete module "${module.name}"?`)) {
      return
    }

    try {
      const response = await apiService.deleteModule(module.id)
      if (response.success) {
        await fetchModules()
      } else {
        setError(response.message || 'Failed to delete module')
      }
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error && 'response' in err
          ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
          : 'Failed to delete module'
      setError(errorMessage || 'Failed to delete module')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Modules Management</h1>
        {canCreate && (
          <button onClick={() => handleOpenModal()} className="btn-primary">
            Add Module
          </button>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-sm text-red-700">{error}</div>
          <button onClick={() => setError('')} className="mt-2 text-sm text-red-600 hover:text-red-800">
            Dismiss
          </button>
        </div>
      )}

      {/* Modules table */}
      <div className="card p-0">
        <div className="table-container">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">Name</th>
                <th className="table-header-cell">Description</th>
                <th className="table-header-cell">Permissions</th>
                <th className="table-header-cell">Created</th>
                <th className="table-header-cell">Actions</th>
              </tr>
            </thead>
            <tbody className="table-body">
              {Array.isArray(modules) &&
                modules.map(module => (
                  <tr key={module.id}>
                    <td className="table-cell">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3">
                          <span className="text-white text-sm font-medium">{module.name[0].toUpperCase()}</span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{module.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900 max-w-xs truncate">{module.description}</div>
                    </td>
                    <td className="table-cell">
                      <span className="text-sm text-gray-900">{module.permissions?.length || 0} permissions</span>
                    </td>
                    <td className="table-cell">{new Date(module.createdAt).toLocaleDateString()}</td>
                    <td className="table-cell">
                      <div className="flex space-x-2">
                        {canUpdate && (
                          <button
                            onClick={() => handleOpenModal(module)}
                            className="text-blue-600 hover:text-blue-900 text-sm"
                          >
                            Edit
                          </button>
                        )}
                        {canDelete && (
                          <button
                            onClick={() => handleDelete(module)}
                            className="text-red-600 hover:text-red-900 text-sm"
                          >
                            Delete
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>

          {modules.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No modules found.</p>
            </div>
          )}
        </div>
      </div>

      {/* Module form modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingModule ? 'Edit Module' : 'Add Module'}
        size="md"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="name" className="form-label">
              Module Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              className={`form-input ${
                formErrors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.name}
              onChange={handleInputChange}
              required
            />
            {formErrors.name && <p className="mt-1 text-sm text-red-600">{formErrors.name}</p>}
          </div>

          <div>
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              className={`form-input ${
                formErrors.description ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
              }`}
              value={formData.description}
              onChange={handleInputChange}
              required
            />
            {formErrors.description && <p className="mt-1 text-sm text-red-600">{formErrors.description}</p>}
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button type="button" onClick={handleCloseModal} className="btn-secondary">
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  {editingModule ? 'Updating...' : 'Creating...'}
                </>
              ) : editingModule ? (
                'Update Module'
              ) : (
                'Create Module'
              )}
            </button>
          </div>
        </form>
      </Modal>
    </div>
  )
}

export default Modules
